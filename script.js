// Les bibliothèques sont chargées via CDN dans index.html

document.addEventListener("DOMContentLoaded", () => {
    console.log("Loaded");

    gsap.registerPlugin(CustomEase, SplitText);
    CustomEase.create("hop", ".87, 0, .13, 1");

    const lenis = new Lenis();
    function raf(time) {
        lenis.raf(time);
        requestAnimationFrame(raf);
    }
    requestAnimationFrame(raf);

    const textContainers = document.querySelectorAll(".menu-col");
    let splitTextByContainer = [];

    textContainers.forEach((container) => {
        const textElements = container.querySelectorAll("a, p");
        let containerSplits = [];

        textElements.forEach((element) => {
            const split = SplitText.create(element, {
                type: "lines",
                mask: "lines",
                linesClass: "line",
            });
            containerSplits.push(split);

            gsap.set(split.lines, { y: "-110%"});
        });

        splitTextByContainer.push(containerSplits);
    });

    const container = document.querySelector(".container");
    const menuToggleBtn = document.querySelector(".menu-toggle-btn");
    const menuOverlay = document.querySelector(".menu-overlay");
    const menuOverlayContainer = document.querySelector(".menu-overlay-content");
    const menuMediaWrapper = document.querySelector(".menu-media-wrapper");
    const menuToggleLabel = document.querySelector(".menu-toggle-label p");

    let isMenuOpen = false;
    let isAnimating = false;

    function openMenu() {
        isMenuOpen = true;
        container.style.pointerEvents = "none";
    }

    function closeMenu() {
        isMenuOpen = false;
        container.style.pointerEvents = "auto";
        lenis.start();
    }

    menuToggleBtn.addEventListener("click", () => {
        if (isAnimating) return;
        
        if (!isMenuOpen) {
            // Ouvrir le menu
            isAnimating = true;
            openMenu();
            lenis.stop();

            const tl = gsap.timeline();

            tl.to(menuToggleLabel, {
                y: "-110%",
                duration: 1,
                ease: "hop",
            }).to(container, {
                y: "-100svh",
                duration: 1,
                ease: "hop",
            }, "<")
            .to(menuOverlay, {
                clipPath: "polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)",
                duration: 1,
                ease: "hop",
            }, "<")
            .to(menuOverlayContainer, {
                yPercent: 0,
                duration: 1,
                ease: "hop",
            }, "<")
            .to(menuMediaWrapper, {
                opacity: 1,
                duration: 0.75,
                ease: "power2.out",
                delay: 0.5,
            }, "<");

            splitTextByContainer.forEach((containerSplits) => {
                const copyLines = containerSplits.flatMap((split => split.lines));
                tl.to(
                    copyLines,
                    {
                        y: "0%",
                        duration: 2,
                        ease: "hop",
                        stagger: -0.075,
                    },
                    -0.15
                );
            });

            tl.call(() => {
                isAnimating = false;
            });

        } else {
            // Fermer le menu
            isAnimating = true;
            closeMenu();

            const tl = gsap.timeline();

            // Animation de fermeture
            splitTextByContainer.forEach((containerSplits) => {
                const copyLines = containerSplits.flatMap((split => split.lines));
                tl.to(
                    copyLines,
                    {
                        y: "-110%",
                        duration: 1,
                        ease: "hop",
                        stagger: 0.075,
                    },
                    0
                );
            });

            tl.to(menuMediaWrapper, {
                opacity: 0,
                duration: 0.5,
                ease: "power2.out",
            }, 0)
            .to(menuOverlayContainer, {
                yPercent: -100,
                duration: 1,
                ease: "hop",
            }, 0.25)
            .to(menuOverlay, {
                clipPath: "polygon(0% 0%, 100% 0%, 100% 0%, 0% 0%)",
                duration: 1,
                ease: "hop",
            }, 0.25)
            .to(container, {
                y: "0",
                duration: 1,
                ease: "hop",
            }, 0.25)
            .to(menuToggleLabel, {
                y: "0%",
                duration: 1,
                ease: "hop",
            }, 0.25)
            .call(() => {
                isAnimating = false;
            });
        }
    });
});
