:root {
    --bg: #171717;
    --fg: #fff;
    --menu-bg: #0f0f0f;
    --menu-fg-secondary: #5f5f5f;
    --hamburger-icon-border: rgba(255, 255, 255, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {

}

img {
    width: 100%;
    height: auto;
    object-fit: cover;
}

a {
    text-decoration: none;
    color: var(--fg);
}

.container {
    position: relative;
    transform: translateY(0svh);
    background-color: var(--bg);
    color: var(--fg);
}

section {
    position: relative;
    width: 100vw;
    height: 100svh;
    padding: 2rem;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    z-index: -1;
}

section h1 {
    width: 75%;
}

section img {
    opacity: 0.5;
}

nav {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100svh;
    pointer-events: none;
    overflow: hidden;
    z-index: 2;
}

.menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    padding: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    pointer-events: all;
    color: var(--menu-fg-secondary);
    z-index: 2;
}

.menu-logo {
    width: 2rem;
    height: 2rem;
}

.menu-toggle-btn {
    display: flex;
    align-items: center;
    gap: 1rem;
    cursor: pointer;
}

.menu-toggle-label {
    overflow: hidden;
}

.menu-toggle-label p {
    position: relative;
    transform: translateY(0%);
    will-change: transform;
}

.menu-hamburger-icon {
    position: relative;
    width: 3rem;
    height: 3rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border: 1px solid var(--hamburger-icon-border);
    border-radius: 100%;
}

.menu-hamburger-icon span {
    position: absolute;
    width: 15px;
    height: 1.25px;
    background-color: var(--fg);
    transition: all 0.75s cubic-bezier(0.87, 0, 0.13, 1);
    transform-origin: center;
    will-change: transform;
}

.menu-hamburger-icon span:nth-child(1) {
    transform: translateY(-3px);
}

.menu-hamburger-icon span:nth-child(2) {
    transform: translateY(3px);
}

.menu-hamburger-icon.active span:nth-child(1) {
    transform: translateY(0) rotate(45deg) scaleX(1.05);
}

.menu-hamburger-icon.active span:nth-child(2) {
    transform: translateY(0) rotate(-45deg) scaleX(1.05);
}

.menu-overlay, .menu-overlay-content {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100svh;
    color: var(--fg);
    overflow: hidden;
    z-index: 1;
}

.menu-overlay {
    background-color: var(--menu-bg);
    clip-path: polygon(0% 0%, 100% 0%, 100% 0%, 0% 0%);
    will-change: clip-path;
}

.menu-overlay-content {
    display: flex;
    transform: translateY(-50%);
    will-change: transform;
    pointer-events: all;
}

.menu-media-wrapper {
    flex: 2;
    /* opacity: 0; */
    will-change: opacity;
}

.menu-media-wrapper img {
    opacity: 0.25;
}

.menu-content-wrapper {
    flex: 3;
    position: relative;
    display: flex;
}

.menu-content-main {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.menu-footer {
    margin: 0 auto;
}

.menu-content-main, .menu-footer {
    width: 75%;
    padding: 2rem;
    display: flex;
    align-items: flex-end;
    gap: 2rem;
}

.menu-col {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.menu-col:nth-child(1) {
    flex: 3;
}

.menu-col:nth-child(2) {
    flex: 2;
}

.menu-link a {
    font-size: 3.5rem;
    font-weight: 500;
    line-height: 1.2;
}

.menu-tag a, .menu-footer p {
    color: var(--menu-fg-secondary);
}

.line {
    position: relative;
    will-change: transform;
}

@media (max-width: 1000px) {
    h1 {
        font-size: 3rem;
    }

    section h1 {
        width: 100%;
    }

    .menu-media-wrapper {
        display: none;
    }

    .menu-content-main,
    .menu-footer {
        width: 100%;
    }

    .menu-content-main {
        top: 50%;
        flex-direction: column;
        align-items: flex-start;
        gap: 5rem;
    }

    .menu-link a {
        font-size: 3rem;
    }

    .menu-tag a {
        font-size: 1.25rem;
    }
}